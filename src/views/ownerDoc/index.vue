<template>
  <div class="app-container">
    <common-layout
      :cols="[
        { colProps: { span: 4 }, slot: 'left', border: { right: true } },
        { colProps: { span: 20 }, slot: 'right' },
      ]"
    >
      <template #left>
        <!-- 树形组件 -->
        <common-tree
          :tree-data="treeData"
          node-key="cateId"
          :highlightCurrent="true"
          :currentNodeKey="selectedCate.cateId"
          @node-click="handlerTreeClick"
        >
          <template #custom="{ data }">
            <span>{{ data.cateName }}</span>
          </template>
        </common-tree>
      </template>
      <template #right>
        <CoustomTable
          :page-buttons-json="pageButtonsJson"
          :paginations="defaultQueryParams"
          :query-form-json="queryFormJson"
          :table-data="tableData"
          :table-json="tableJson"
          :total="total"
          @onButton="handlerButton"
          @onPageButton="handlerPageButton"
          @onPagination="handlerPagination"
          @onSearch="handlerSearch"
          @onTableRowClick="handlerTableRowClick"
        />
      </template>

      <CoustomTableDialog
        ref="formDialog"
        :config="dialogJson"
        :init-data="{ defaultSelectId: '' }"
        :visible.sync="dialogVisible"
        @onSubmit="handlerSubmit"
      />
    </common-layout>
  </div>
</template>
<script>
import CoustomTable from '@/components/CoustomTable';
import CoustomFormDialog from '@/components/CoustomFormDialog';
import CoustomTableDialog from '@/components/CoustomTableDialog';
import {
  TableJson,
  QueryFormJson,
  PageButtonsJson,
  DialogJson,
  InputDialogDialogJson,
  domeData,
} from './constants';
import {
  getDevApiIotTopologyList,
  postDevApiIotTopology,
  getDevApiIotTopologyTopologyId,
  deleteDevApiIotTopologyTopologyId,
  putDevApiIotTopology,
} from '@/api/iot/ClassesTopology';
import { postBpmFileList } from '@/api/v2/file/file-controller';
import CommonTree from '@/components/CommonTree';
import pageMixin from '@/mixin/page';
import CommonLayout from '@/components/CommonLayout';
import { getBpmFileCategoryList } from '@/api/v2/file/flile-category-controller';
// 业主发文 cateId
const defaultCateIdId = 1;
export default {
  name: 'ClassesTopologyMap',
  components: {
    CoustomTable,
    CoustomTableDialog,
    CommonTree,
    CommonLayout,
  },
  mixins: [pageMixin],
  dicts: ['iot_class_type', 'iot_access_type'],
  provide() {
    return {
      dictData: this.dict,
    };
  },
  data() {
    return {
      queryFormJson: QueryFormJson,
      pageButtonsJson: PageButtonsJson,
      tableJson: TableJson,
      dialogJson: InputDialogDialogJson,
      associationFields: ['className', 'cateId'],
      // 不需要分页
      hiddenPagination: false,
      // 父节点 ID
      parentId: 0,
      treeData: [],
      selectedCate: null,
    };
  },
  computed: {
    packageName() {
      const { query } = this.$route;
      return query.packageName;
    },
  },
  created() {},
  async mounted() {
    await this.getTreeData();
    console.log('this.selectedCate----', this.selectedCate);
    this.getList(
      { className: this.selectedCate?.procdef?.className, cateId: this.selectedCate?.cateId },
      postBpmFileList,
    );
  },

  methods: {
    handlerTreeClick(data) {
      if (data.cateId === defaultCateIdId) return;
      this.selectedCate = data;
      this.getList({ className: data.procdef?.className, cateId: data.cateId }, postBpmFileList);
    },

    async getTreeData() {
      const res = await getBpmFileCategoryList();
      const treeData = this.handleTree(res.rows, 'cateId');
      // 过滤掉根节点, 只保留业主发文
      this.treeData = treeData.filter(item => item.cateId === defaultCateIdId);
      // 默认选中第一个数据
      this.selectedCate = this.treeData?.[0]?.children?.[0] || {};
      console.log('first', this.treeData);
    },

    handleResponse(rows = []) {
      return rows.map(item => {
        const objectsProperites = item.objectsProperites;
        objectsProperites.forEach(el => {
          item[el.propertyName] = el.propertyValue;
        });
        return item;
      });
    },

    async handlerSubmit(formData) {
      const { packageName, classId, className } = formData;
      if (this.parentId === classId) {
        this.$message.error('选中的类不能与父类相同！');
        return;
      }

      this.handlerSave({
        formData: {
          parentId: this.parentId,
          packageName,
          classId,
          className,
        },
        apis: {
          addApi: postDevApiIotTopology,
          editApi: putDevApiIotTopology,
        },
        editParams: ['topologyId'],
      });
    },

    handlerTableRowClick(item) {
      this.goToPage({
        path: '/carbon-iot/packages/classes/detail',
        query: {
          className: item.className,
        },
      });
    },

    // 新增按钮事件
    handlerPageButton(item) {
      if (item.type === 'Add') {
        this.parentId = 0;
      }

      if (!this.selectedCate) {
        this.$message.error('请先选择分类');
        return;
      }

      console.log('first', item);
      const { procdef, cateId, cateName } = this.selectedCate || {};
      const { deploymentId, processService, formDataPath, fdId, procdefId, classId, className } =
        procdef || {};
      this.$router.push({
        path: '/carbon-flowable/work/start',
        query: {
          cateName,
          cateId,
          deployId: deploymentId,
          definitionId: procdefId,
          processService,
          formDataPath,
          fdId,
          classId,
          className,
        },
      });
      // this.pageButton({ item });
    },

    async getInfo(id) {
      this.getInfoData({ id, api: getDevApiIotTopologyList });
    },

    deleteItem(id) {
      this.deleteTableItem({ id, api: deleteDevApiIotTopologyTopologyId });
    },

    handlerButton(btnItem) {
      if (btnItem.item.type === 'Info') {
        console.log('btnItem---', btnItem);
      }
      // this.tableButton({ btnItem, idKey: 'topologyId' });
    },
  },
};
</script>
<style scoped lang="scss">
.app-container {
  height: 90vh;
}
</style>
